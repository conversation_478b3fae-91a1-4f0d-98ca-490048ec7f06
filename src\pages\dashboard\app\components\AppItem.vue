<template>
  <div class="app-item-wrap" :class="{'add-item': type === 'add'}">
    <div class="app-icon">
      <img :src="appItem.icon" alt="">
    </div>
    <div class="app-item">
      <div class="app-item-content">
        <h3 class="item-title">{{ appItem.title }}</h3>
        <p class="item-desc">{{ appItem.remarks }}</p>
      </div>
      <div class="app-item-btn" v-if="type === 'add'" @click="handleAddClick">
        <img class="icon" src="@/assets/image/dashboard/app/add.png" alt="">
        <span>添加应用</span>
      </div>
      <div class="app-item-btn" v-else @click="openApp">
        <span>打开应用</span>
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
    
  </div>
</template>

<script>

export default {
  name: "AppItem",
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: 'item'  // add:添加  item:正常列表
    }
  },
  computed: {
    appItem() {
      return this.type === 'add' ? this.addItem : this.item
    }
  },
  data() {
    return {
      addItem: {
        icon: require('@/assets/image/dashboard/app/app.png'),
        title: '应用名称',
        desc: '此处为应用的简单介绍，字数建议控制在40字以内'
      }
    }
  },
  methods: {
    openApp() {
      if (!this.item.url) return
      window.open(this.item.url, '_blank')
    },
    handleAddClick() {
      // 触发添加应用事件
      this.$emit('click')
    }
  }
};
</script>

<style scoped lang="scss">
.app-item-wrap {
  .app-icon {
    width: 60px;
    height: 60px;
    background: #FFFFFF;
    box-shadow: 1px 6px 10px 0px rgba(81,131,246,0.1);
    border-radius: 14px;
    position: relative;
    z-index: 10;
    margin-left: 26px;
    img {
      width: 100%;
      height: 100%;
      object-fit: scale-down;
    }
  }
  .app-item {
    height: 210px;
    background: #FFFFFF;
    border-radius: 12px;
    cursor: pointer;
    position: relative;
    z-index: 6;
    padding: 60px 24px 27px 24px;
    box-sizing: border-box;
    margin-top: -38px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .app-item-content {
      .item-title {
        font-size: 16px;
        color: #111111;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 10px;
      }
      .item-desc {
        font-size: 12px;
        color: #8F8F8F;
        line-height: 20px;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .app-item-btn {
      width: 96px;
      height: 30px;
      background: #5183F6;
      border-radius: 4px;
      font-size: 13px;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        font-size: 12px;
        margin-left: 4px;
      }
      span {
        margin-left: 4px;
      }
    }
  }
  &.add-item {
    .app-item {
      background: #F9FBFF;
      border: 1px dashed #AECDFF;
      .app-item-btn {
        width: 100%;
        background: #F9FBFF;
        border: 1px dashed #1F72F5;
        color: #1F72F5;
      }
    }
  }
}
</style>