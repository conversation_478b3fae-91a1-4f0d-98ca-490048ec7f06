<template>
  <div class="app-list-wrapper">
    <app-item v-if="showAdd" type="add" @click="handleAddApp" />
    <AppItem v-for="(item, index) in list" :key="index" :item="item" />
    <AddApp ref="addApp" @success="handleSuccess" />
  </div>
</template>

<script>
import AppItem from './AppItem.vue'
import AddApp from './AddApp.vue'
import { getThirdAppListApi } from '@/api/consoleNew'

export default {
  name: "AppList",
  components: {
    AppItem,
    AddApp
  },
  data() {
    return {
      showAdd: false,
      list: [
        // {
        //   icon: require('@/assets/image/dashboard/app/lc.png'),
        //   title: '龙采erp',
        //   desc: '智能一体化企业管理系统，助力降本增效、数字化升级！',
        //   link: 'https://erp1.ailongcai.com:12973/pc/finance/index/'
        // },
        // {
        //   icon: require('@/assets/image/dashboard/app/qd.png'),
        //   title: '抢单平台',
        //   desc: '极速抢单平台，智能匹配供需，让赚钱快人一步！',
        //   link: 'https://flexible.china9.cn/enterprise/'
        // },
        // {
        //   icon: require('@/assets/image/dashboard/app/dls.png'),
        //   title: '代理商平台',
        //   desc: '全链路赋能代理，智能撮合商机，共赢增长新生态！',
        //   link: 'https://w2.china9.cn/'
        // },
      ]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await getThirdAppListApi()
      console.log(res, '三方应用列表')
      if (res.code === 200) {
        const { list, power } = res.data
        this.list = list
        this.showAdd = power
      }
    },
    handleAddApp() {
      // 打开添加应用弹窗
      this.$refs.addApp.visible = true
    },
    handleSuccess() {
      // 保存成功后刷新列表
      this.getList()
    }
  }
};
</script>

<style scoped lang="scss">
.app-list-wrapper {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  flex-wrap: wrap;
}
@media screen and (max-width: 1500px) {
  .app-list-wrapper {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media screen and (max-width: 1300px) {
  .app-list-wrapper {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media screen and (max-width: 1100px) {
  .app-list-wrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 900px) {
  .app-list-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>