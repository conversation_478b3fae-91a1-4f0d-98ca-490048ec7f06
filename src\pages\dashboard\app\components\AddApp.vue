<template>
  <el-dialog :title="title" :visible.sync="visible" width="500px">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="LOGO" prop="icon">
      </el-form-item>
      <el-form-item label="名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入应用名称" clearable />
      </el-form-item>
      <el-form-item label="网址" prop="url">
        <el-input v-model="form.url" placeholder="请输入应用网址" clearable />
      </el-form-item>
      <el-form-item label="描述" prop="remarks">
        <el-input type="textarea" v-model="form.remarks" placeholder="请输入应用描述" :rows="4" :maxlength="40" show-word-limit resize="none" clearable />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "AddApp",
  data() {
    return {
      visible: false,
      form: {
        title: '',
        icon: '',
        remarks: '',
        url: ''
      },
      rules: {
        title: [{required: true, message: '请输入应用名称', trigger: 'blur'}],
        icon: [{required: true, message: '请上传应用图标', trigger: 'blur'}],
        remarks: [{required: true, message: '请输入应用描述', trigger: 'blur'}],
        url: [
          { required: true, message: '请输入应用链接', trigger: 'blur' },
          { validator: (rule, value, callback) => {
              if (!/^https?:\/\/.*/.test(value)) {
                callback(new Error('请输入正确的链接'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    };
  }
};
</script>