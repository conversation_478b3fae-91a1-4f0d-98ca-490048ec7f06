<template>
  <div class="logo-upload-wrapper">
    <el-upload
      name="files[]"
      class="avatar-uploader"
      :data="{token: token}"
      :action="uploadUrl"
      :show-file-list="false"
      :on-success="handleSuccess"
      :disabled="disabled"
      accept="image/*"
    >
      <el-image v-if="logoUrl" fit="contain" :src="logoUrl" class="avatar"/>
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
  </div>
</template>

<script>
export default {
  name: 'LogoUpload',
  props: {
    // 当前logo的值
    value: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 自定义上传地址
    uploadUrl: {
      type: String,
      default: process.env.BASE_API + 'api/upload'
    }
  },
  data() {
    return {
      logoUrl: ''
    }
  },
  computed: {
    token() {
      return (
        this.$cookies.get('token') ||
        this.$cookies.get('access_token') ||
        this.$store.getters.token
      )
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.logoUrl = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleSuccess(res, file) {
      if (res.data && res.data.length) {
        // 更新显示的图片
        this.logoUrl = URL.createObjectURL(file.raw)
        // 向父组件传递上传成功的文件信息
        this.$emit('input', res.data[0])
        this.$emit('success', res.data[0], this.logoUrl)
      }
    }
  }
}
</script>

<style scoped>
.logo-upload-wrapper >>> .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.logo-upload-wrapper >>> .avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.logo-upload-wrapper >>> .avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
